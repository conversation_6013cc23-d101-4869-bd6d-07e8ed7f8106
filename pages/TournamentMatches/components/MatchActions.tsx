import React from "react";
import { Pressable, View } from "react-native";
import { Match } from "../../../types/matches";
import { Icon } from "@/components/ui/icon";
import { Edit2Icon, Trash2Icon, PlayIcon } from "lucide-react-native";
import SlideButton from "@/components/k-components/SlideButton";

interface MatchActionsProps {
  match: Match;
}

const SettingIcon = ({
  onPress,
  icon,
  color,
}: {
  onPress: () => void;
  icon: any;
  color: string;
}) => {
  return (
    <Pressable
      onPress={onPress}
      className={`p-3 border border-${color} rounded-full`}
    >
      <Icon as={icon} size="lg" className={`text-${color}`} />
    </Pressable>
  );
};

const MatchActions: React.FC<MatchActionsProps> = ({ match }) => {
  const handleStartMatch = () => {
    // TODO: Implement start match logic
    console.log("Starting match:", match.id);
  };

  return (
    <View className="flex-1">
      <View className="flex-row justify-end gap-3">
        <SettingIcon onPress={() => {}} icon={Edit2Icon} color="gray-400" />
        <SettingIcon onPress={() => {}} icon={Trash2Icon} color="red-500" />
      </View>

      {/* Slide Button for Start Match */}
      <View className="mt-4 items-center">
        <SlideButton
          onSlideComplete={handleStartMatch}
          text="Slide to Start Match"
          backgroundColor="bg-primary-0"
          textColor="text-white"
          icon={PlayIcon}
          width={260}
          height={52}
        />
      </View>
    </View>
  );
};

export default MatchActions;
