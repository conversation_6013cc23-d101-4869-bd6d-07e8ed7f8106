import React from "react";
import { Pressable, View } from "react-native";
import { Match } from "../../../types/matches";
import { Icon } from "@/components/ui/icon";
import { Edit2Icon, Trash2Icon } from "lucide-react-native";

interface MatchActionsProps {
  match: Match;
}

const SettingIcon = ({ onPress, icon, color }: { onPress: () => void, icon: any; color: string;
 }) => {
  return (
    <Pressable onPress={onPress} className="p-3 border border-gray-300 rounded-full">
      <Icon as={icon} size="lg" className="text-primary-0" />
    </Pressable>
  );
};

const MatchActions: React.FC<MatchActionsProps> = ({ match }) => {
  return (
    <View className="flex-1">
      <View className="flex-row justify-end gap-3">
        <View className="p-3 border border-gray-300 rounded-full">
          <Icon as={Edit2Icon} size="lg" className="text-primary-0" />
        </View>
        <View className="p-3 border border-gray-300 rounded-full">
          <Icon as={Trash2Icon} size="lg" className="text-red-500" />
        </View>
      </View>
    </View>
  );
};

export default MatchActions;
