import React from "react";
import { View } from "react-native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  Easing,
} from "react-native-reanimated";

interface PingIndicatorProps {
  isActive: boolean;
  children: React.ReactNode;
  className?: string;
  pingColor?: string;
}

const PingIndicator: React.FC<PingIndicatorProps> = ({
  isActive,
  children,
  className = "",
  pingColor = "bg-green-400",
}) => {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(0.75);

  React.useEffect(() => {
    if (isActive) {
      // Start ping animation
      scale.value = withRepeat(
        withTiming(1.5, {
          duration: 1000,
          easing: Easing.out(Easing.quad),
        }),
        -1,
        false
      );

      opacity.value = withRepeat(
        withTiming(0, {
          duration: 1000,
          easing: Easing.out(Easing.quad),
        }),
        -1,
        false
      );
    } else {
      // Reset animation
      scale.value = withTiming(1, { duration: 200 });
      opacity.value = withTiming(0, { duration: 200 });
    }
  }, [isActive]);

  const animatedPingStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  if (!isActive) {
    return <View className={className}>{children}</View>;
  }

  return (
    <View className={`relative ${className}`}>
      {/* Ping animation layer */}
      <Animated.View
        style={[
          {
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            borderRadius: 9999, // rounded-full equivalent
          },
          animatedPingStyle,
        ]}
        className={`${pingColor} opacity-75`}
      />

      {/* Content layer */}
      <View className="relative">{children}</View>
    </View>
  );
};

export default PingIndicator;
