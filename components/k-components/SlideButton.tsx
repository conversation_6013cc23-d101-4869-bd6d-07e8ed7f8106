import React, { useState } from "react";
import { View, Text } from "react-native";
import {
  GestureDetector,
  GestureHandlerRootView,
} from "react-native-gesture-handler";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  runOnJS,
  withSpring,
  interpolate,
  Extrapolation,
} from "react-native-reanimated";
import { Gesture } from "react-native-gesture-handler";
import { triggerHapticFeedback } from "@/utils";
import { ArrowRightIcon } from "lucide-react-native";
import { Icon } from "@/components/ui/icon";

interface SlideButtonProps {
  onSlideComplete: () => void;
  text?: string;
  backgroundColor?: string;
  textColor?: string;
  sliderColor?: string;
  disabled?: boolean;
  width?: number | "auto";
  height?: number;
  borderRadius?: number;
  icon?: React.ComponentType<any>;
}

const SlideButton: React.FC<SlideButtonProps> = ({
  onSlideComplete,
  text = "Slide to confirm",
  backgroundColor = "bg-primary-0",
  textColor = "text-white",
  sliderColor = "bg-white",
  disabled = false,
  width = "auto",
  height = 56,
  borderRadius = 28,
  icon: IconComponent = ArrowRightIcon,
}) => {
  const [isCompleted, setIsCompleted] = useState(false);
  const [containerWidth, setContainerWidth] = useState(280);
  const translateX = useSharedValue(0);
  const opacity = useSharedValue(1);
  const scale = useSharedValue(1);

  const sliderWidth = height - 8; // Slider is slightly smaller than container
  const maxTranslate = containerWidth - sliderWidth - 8; // Account for padding

  const panGesture = Gesture.Pan()
    .onStart(() => {
      if (disabled || isCompleted) return;
      runOnJS(triggerHapticFeedback)();
      scale.value = withSpring(0.95);
    })
    .onUpdate((event) => {
      if (disabled || isCompleted) return;

      const clampedTranslateX = Math.max(
        0,
        Math.min(event.translationX, maxTranslate)
      );
      translateX.value = clampedTranslateX;

      // Trigger haptic feedback when approaching the end
      if (clampedTranslateX > maxTranslate * 0.8) {
        runOnJS(triggerHapticFeedback)();
      }
    })
    .onEnd((event) => {
      if (disabled || isCompleted) return;

      scale.value = withSpring(1);

      if (event.translationX > maxTranslate * 0.7) {
        // Slide completed
        translateX.value = withSpring(maxTranslate);
        opacity.value = withSpring(0);
        runOnJS(triggerHapticFeedback)();
        runOnJS(setIsCompleted)(true);
        runOnJS(onSlideComplete)();
      } else {
        // Slide back to start
        translateX.value = withSpring(0);
      }
    });

  const sliderAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: translateX.value }, { scale: scale.value }],
    };
  });

  const textAnimatedStyle = useAnimatedStyle(() => {
    const textOpacity = interpolate(
      translateX.value,
      [0, maxTranslate * 0.3],
      [1, 0],
      Extrapolation.CLAMP
    );

    return {
      opacity: textOpacity,
    };
  });

  const backgroundAnimatedStyle = useAnimatedStyle(() => {
    const backgroundOpacity = interpolate(
      translateX.value,
      [0, maxTranslate],
      [1, 0.8],
      Extrapolation.CLAMP
    );

    return {
      opacity: backgroundOpacity,
    };
  });

  const successAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
    };
  });

  if (isCompleted) {
    return (
      <Animated.View
        style={[
          {
            width,
            height,
            borderRadius,
            justifyContent: "center",
            alignItems: "center",
          },
          successAnimatedStyle,
        ]}
        className="bg-green-500"
      >
        <Text className="text-white font-urbanistSemiBold text-base">
          ✓ Completed
        </Text>
      </Animated.View>
    );
  }

  const handleLayout = (event: any) => {
    const { width: layoutWidth } = event.nativeEvent.layout;
    setContainerWidth(layoutWidth);
  };

  return (
    <GestureHandlerRootView
      style={{ width: width === "auto" ? "100%" : width }}
    >
      <View
        style={{
          width: width === "auto" ? "100%" : width,
          height,
          borderRadius,
        }}
        className={`relative overflow-hidden ${backgroundColor} ${
          disabled ? "opacity-50" : ""
        }`}
        onLayout={handleLayout}
      >
        {/* Background with animated opacity */}
        <Animated.View
          style={[
            {
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              borderRadius,
            },
            backgroundAnimatedStyle,
          ]}
          className={backgroundColor}
        />

        {/* Text */}
        <Animated.View
          style={[
            {
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              justifyContent: "center",
              alignItems: "center",
            },
            textAnimatedStyle,
          ]}
        >
          <Text className={`font-urbanistSemiBold text-base ${textColor}`}>
            {text}
          </Text>
        </Animated.View>

        {/* Slider */}
        <GestureDetector gesture={panGesture}>
          <Animated.View
            style={[
              {
                position: "absolute",
                top: 4,
                left: 4,
                width: sliderWidth,
                height: sliderWidth,
                borderRadius: sliderWidth / 2,
                justifyContent: "center",
                alignItems: "center",
                shadowColor: "#000",
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.25,
                shadowRadius: 3.84,
                elevation: 5,
              },
              sliderAnimatedStyle,
            ]}
            className={sliderColor}
          >
            <Icon as={IconComponent} size="lg" className="text-primary-0" />
          </Animated.View>
        </GestureDetector>
      </View>
    </GestureHandlerRootView>
  );
};

export default SlideButton;
